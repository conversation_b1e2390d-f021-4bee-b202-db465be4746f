#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIFA手动会话捕获工具
通过手动输入浏览器捕获的请求来分析FIFA注册流程
"""

import json
import re
import logging
from datetime import datetime
from typing import Dict, List
from urllib.parse import urlparse, unquote

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ManualSessionCapture:
    """手动会话捕获分析器"""
    
    def __init__(self):
        self.captured_data = []
        self.analysis_result = {
            'registration_endpoints': [],
            'verification_endpoints': [],
            'required_headers': {},
            'form_fields': {},
            'csrf_tokens': [],
            'cookies': {},
            'api_flow': []
        }
    
    def interactive_capture(self):
        """交互式捕获会话"""
        print("="*60)
        print("FIFA手动会话捕获工具")
        print("="*60)
        print()
        print("请按照以下步骤操作：")
        print()
        print("1. 打开Chrome浏览器")
        print("2. 访问 https://www.fifa.com/account/register")
        print("3. 打开开发者工具 (F12) -> Network 标签页")
        print("4. 开始注册流程（填写表单但先不要提交）")
        print("5. 准备好后，按回车继续...")
        
        input()
        
        print("\n现在请完成以下操作：")
        print("1. 点击注册按钮")
        print("2. 在Network标签页中找到相关的请求")
        print("3. 右键点击请求 -> Copy -> Copy as cURL")
        print("4. 将cURL命令粘贴到下面")
        print()
        
        self.collect_curl_commands()
    
    def collect_curl_commands(self):
        """收集cURL命令"""
        curl_commands = []
        
        print("请粘贴cURL命令（每次粘贴一个，输入'done'结束）：")
        print("-" * 60)
        
        while True:
            print(f"\n请输入第{len(curl_commands)+1}个cURL命令（或输入'done'结束）：")
            curl_input = input().strip()
            
            if curl_input.lower() == 'done':
                break
            
            if curl_input.startswith('curl'):
                curl_commands.append(curl_input)
                print(f"✅ 已添加cURL命令 #{len(curl_commands)}")
            else:
                print("❌ 无效的cURL命令，请重新输入")
        
        if curl_commands:
            print(f"\n收集到 {len(curl_commands)} 个cURL命令")
            self.analyze_curl_commands(curl_commands)
        else:
            print("❌ 未收集到任何cURL命令")
    
    def analyze_curl_commands(self, curl_commands: List[str]):
        """分析cURL命令"""
        print("\n开始分析cURL命令...")
        
        for i, curl_cmd in enumerate(curl_commands, 1):
            print(f"分析命令 {i}/{len(curl_commands)}...")
            request_info = self.parse_curl_command(curl_cmd)
            if request_info:
                self.captured_data.append(request_info)
                self.classify_request(request_info)
        
        self.generate_analysis()
    
    def parse_curl_command(self, curl_cmd: str) -> Dict:
        """解析单个cURL命令"""
        try:
            # 提取URL
            url_match = re.search(r"curl\s+['\"]([^'\"]+)['\"]", curl_cmd)
            if not url_match:
                url_match = re.search(r"curl\s+([^\s]+)", curl_cmd)
            
            if not url_match:
                return None
            
            url = url_match.group(1)
            
            # 只处理FIFA相关的请求
            if 'fifa.com' not in url:
                return None
            
            # 提取请求方法
            method = 'GET'
            if '-X POST' in curl_cmd or '--data' in curl_cmd:
                method = 'POST'
            elif '-X PUT' in curl_cmd:
                method = 'PUT'
            elif '-X DELETE' in curl_cmd:
                method = 'DELETE'
            
            # 提取请求头
            headers = {}
            header_matches = re.findall(r"-H\s+['\"]([^:]+):\s*([^'\"]+)['\"]", curl_cmd)
            for header_name, header_value in header_matches:
                headers[header_name.strip()] = header_value.strip()
            
            # 提取请求数据
            data = None
            data_match = re.search(r"--data-raw\s+['\"]([^'\"]*)['\"]", curl_cmd, re.DOTALL)
            if not data_match:
                data_match = re.search(r"--data\s+['\"]([^'\"]*)['\"]", curl_cmd, re.DOTALL)
            
            if data_match:
                data_str = data_match.group(1)
                try:
                    # 尝试解析JSON
                    data = json.loads(data_str)
                except:
                    # 如果不是JSON，保持原始字符串
                    data = data_str
            
            return {
                'timestamp': datetime.now().isoformat(),
                'method': method,
                'url': url,
                'headers': headers,
                'data': data,
                'curl_command': curl_cmd
            }
            
        except Exception as e:
            logger.error(f"解析cURL命令失败: {e}")
            return None
    
    def classify_request(self, request_info: Dict):
        """分类请求"""
        url = request_info['url'].lower()
        method = request_info['method']
        
        # 注册相关
        if any(keyword in url for keyword in ['register', 'signup', 'create-account']):
            self.analysis_result['registration_endpoints'].append(request_info)
            print(f"  ✅ 发现注册端点: {method} {request_info['url']}")
        
        # 验证相关
        elif any(keyword in url for keyword in ['verify', 'confirm', 'validate', 'otp']):
            self.analysis_result['verification_endpoints'].append(request_info)
            print(f"  ✅ 发现验证端点: {method} {request_info['url']}")
        
        # 提取重要信息
        headers = request_info.get('headers', {})
        
        # CSRF tokens
        for header_name, header_value in headers.items():
            if any(keyword in header_name.lower() for keyword in ['csrf', 'token', 'xsrf']):
                self.analysis_result['csrf_tokens'].append({
                    'header': header_name,
                    'value': header_value,
                    'url': request_info['url']
                })
                print(f"  🔑 发现CSRF令牌: {header_name}")
        
        # Cookies
        if 'Cookie' in headers:
            cookie_str = headers['Cookie']
            cookies = {}
            for cookie in cookie_str.split(';'):
                if '=' in cookie:
                    key, value = cookie.strip().split('=', 1)
                    cookies[key] = value
            self.analysis_result['cookies'].update(cookies)
        
        # 表单字段
        data = request_info.get('data')
        if isinstance(data, dict):
            for field_name, field_value in data.items():
                if field_name not in self.analysis_result['form_fields']:
                    self.analysis_result['form_fields'][field_name] = []
                self.analysis_result['form_fields'][field_name].append(field_value)
                print(f"  📝 发现表单字段: {field_name}")
    
    def generate_analysis(self):
        """生成分析结果"""
        print("\n" + "="*60)
        print("分析结果")
        print("="*60)
        
        # 统计信息
        print(f"总请求数: {len(self.captured_data)}")
        print(f"注册端点: {len(self.analysis_result['registration_endpoints'])}")
        print(f"验证端点: {len(self.analysis_result['verification_endpoints'])}")
        print(f"CSRF令牌: {len(self.analysis_result['csrf_tokens'])}")
        print(f"表单字段: {len(self.analysis_result['form_fields'])}")
        print(f"Cookies: {len(self.analysis_result['cookies'])}")
        
        # 详细信息
        if self.analysis_result['registration_endpoints']:
            print("\n📍 注册端点:")
            for endpoint in self.analysis_result['registration_endpoints']:
                print(f"  {endpoint['method']} {endpoint['url']}")
        
        if self.analysis_result['verification_endpoints']:
            print("\n📍 验证端点:")
            for endpoint in self.analysis_result['verification_endpoints']:
                print(f"  {endpoint['method']} {endpoint['url']}")
        
        if self.analysis_result['form_fields']:
            print("\n📝 表单字段:")
            for field_name, values in self.analysis_result['form_fields'].items():
                print(f"  {field_name}: {values[0] if values else 'N/A'}")
        
        if self.analysis_result['csrf_tokens']:
            print("\n🔑 CSRF令牌:")
            for token in self.analysis_result['csrf_tokens']:
                print(f"  {token['header']}: {token['value'][:20]}...")
        
        # 保存详细结果
        result = {
            'analysis_timestamp': datetime.now().isoformat(),
            'captured_requests': self.captured_data,
            'analysis_result': self.analysis_result
        }
        
        with open('manual_session_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 详细结果已保存: manual_session_analysis.json")
        
        # 生成可用的API代码
        self.generate_api_code()
    
    def generate_api_code(self):
        """生成API调用代码"""
        if not self.analysis_result['registration_endpoints']:
            print("❌ 未发现注册端点，无法生成API代码")
            return
        
        reg_endpoint = self.analysis_result['registration_endpoints'][0]
        
        # 提取通用请求头
        common_headers = {}
        for req in self.captured_data:
            headers = req.get('headers', {})
            for header_name, header_value in headers.items():
                if header_name.lower() not in ['content-length', 'cookie', 'content-type']:
                    common_headers[header_name] = header_value
        
        # 生成代码
        code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于手动会话捕获的FIFA注册API
生成时间: {datetime.now().isoformat()}
"""

import requests
import json
from typing import Dict

class FIFARegistrationAPI:
    """FIFA注册API"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_headers()
    
    def setup_headers(self):
        """设置请求头"""
        headers = {{
{self.format_headers(common_headers)}
        }}
        self.session.headers.update(headers)
    
    def register_account(self, user_data: Dict) -> Dict:
        """注册账号"""
        url = "{reg_endpoint['url']}"
        
        try:
            response = self.session.{reg_endpoint['method'].lower()}(
                url,
                json=user_data,
                timeout=15
            )
            
            return {{
                'success': response.status_code in [200, 201],
                'status_code': response.status_code,
                'response': response.text[:500]
            }}
            
        except Exception as e:
            return {{
                'success': False,
                'error': str(e)
            }}

# 测试代码
if __name__ == "__main__":
    api = FIFARegistrationAPI()
    
    test_data = {{
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "firstName": "Test",
        "lastName": "User"
    }}
    
    result = api.register_account(test_data)
    print(f"注册结果: {{result}}")
'''
        
        with open('fifa_api_from_capture.py', 'w', encoding='utf-8') as f:
            f.write(code)
        
        print(f"🚀 API代码已生成: fifa_api_from_capture.py")
    
    def format_headers(self, headers: Dict) -> str:
        """格式化请求头"""
        lines = []
        for header_name, header_value in headers.items():
            lines.append(f'            "{header_name}": "{header_value}",')
        return '\n'.join(lines)

def main():
    """主函数"""
    capture = ManualSessionCapture()
    capture.interactive_capture()

if __name__ == "__main__":
    main()
