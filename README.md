# FIFA自动注册工具

基于协议逆向的高效FIFA账号自动注册工具，支持批量注册和邮箱验证。

## 功能特点

- ✅ **协议逆向**: 直接调用FIFA API，效率远超浏览器自动化
- ✅ **批量注册**: 支持批量创建多个FIFA账号
- ✅ **邮箱集成**: 自动接收和处理验证邮件
- ✅ **域名邮箱**: 支持自定义域名邮箱（非Gmail/Outlook）
- ✅ **反检测**: 模拟真实用户行为，避免被检测
- ✅ **智能重试**: 多端点尝试，提高成功率
- ✅ **详细日志**: 完整的操作日志和错误追踪

## 文件结构

```
fifa账号注册/
├── email_manager.py          # 邮箱管理核心模块
├── fifa_registration_bot.py  # FIFA注册机器人
├── fifa_protocol_analyzer.py # FIFA协议分析工具
├── fifa_api_interceptor.py   # API拦截分析工具
├── test_email_receiver.py    # 邮箱功能测试
├── test_fifa_registration.py # FIFA注册测试
├── email_config.json         # 邮箱配置文件
└── README.md                 # 使用说明
```

## 快速开始

### 1. 环境准备

确保已安装Python 3.7+和必要的依赖：

```bash
pip install requests selenium-driverless
```

### 2. 配置邮箱

首先运行邮箱测试工具配置您的域名邮箱：

```bash
python test_email_receiver.py
```

按照提示配置您的IMAP邮箱设置，工具会自动生成 `email_config.json` 配置文件。

### 3. 测试注册

运行FIFA注册测试：

```bash
python test_fifa_registration.py
```

选择相应的测试选项：
- **选项1**: 测试邮箱连接
- **选项2**: 单账号注册测试  
- **选项3**: 批量注册测试

## 详细使用说明

### 邮箱配置

编辑 `email_config.json` 文件，添加您的域名邮箱配置：

```json
{
  "email_accounts": [
    {
      "email": "<EMAIL>",
      "password": "your_email_password",
      "imap_server": "imap.yourdomain.com",
      "imap_port": 993,
      "use_ssl": true
    },
    {
      "email": "<EMAIL>", 
      "password": "your_email_password",
      "imap_server": "imap.yourdomain.com",
      "imap_port": 993,
      "use_ssl": true
    }
  ]
}
```

### 批量注册

使用 `FIFARegistrationBot` 类进行批量注册：

```python
from fifa_registration_bot import FIFARegistrationBot
from email_manager import EmailManager

# 初始化
email_manager = EmailManager()
bot = FIFARegistrationBot(email_manager)

# 邮箱列表
email_list = [
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>"
]

# 批量注册
results = bot.batch_register(email_list)

# 保存结果
bot.save_results(results)
```

### 单个注册

```python
# 生成随机用户数据
user_data = bot.generate_random_user_data()

# 注册单个账号
result = bot.register_account("<EMAIL>", user_data)

if result['success']:
    print("注册成功!")
else:
    print(f"注册失败: {result['error']}")
```

## 核心模块说明

### EmailManager (邮箱管理器)

负责处理邮箱连接、验证码接收和邮件解析：

- `get_verification_code()`: 获取验证码
- `connect_to_email()`: 连接邮箱服务器
- `extract_verification_code()`: 提取验证码

### FIFARegistrationBot (注册机器人)

核心注册逻辑，包含：

- `register_account()`: 注册单个账号
- `batch_register()`: 批量注册
- `handle_email_verification()`: 处理邮箱验证
- `generate_random_user_data()`: 生成随机用户数据

### 协议分析工具

- `fifa_protocol_analyzer.py`: 静态分析FIFA注册页面
- `fifa_api_interceptor.py`: 动态拦截API调用（需要浏览器）

## 技术原理

### 协议逆向方法

1. **静态分析**: 分析FIFA注册页面的HTML和JavaScript
2. **动态拦截**: 使用浏览器拦截真实的API调用
3. **端点发现**: 识别注册相关的API端点
4. **参数分析**: 分析请求参数和响应格式
5. **会话管理**: 处理cookies、CSRF token等认证信息

### 反检测策略

- 随机用户代理和请求头
- 模拟真实的请求时序
- 随机延迟和重试机制
- 设备指纹伪造

### 邮箱验证流程

1. 发送注册请求
2. 监听邮箱收件箱
3. 提取验证码（支持多种格式）
4. 自动完成验证流程

## 注意事项

### 使用限制

- 请遵守FIFA的服务条款
- 建议合理控制注册频率
- 仅用于学习和研究目的

### 技术限制

- FIFA可能更新API接口，需要相应调整
- 部分功能依赖于邮箱服务的稳定性
- 网络环境可能影响成功率

### 安全建议

- 使用专用的域名邮箱
- 定期更新User-Agent等请求头
- 监控日志文件，及时发现问题

## 故障排除

### 常见问题

1. **邮箱连接失败**
   - 检查IMAP设置是否正确
   - 确认邮箱密码和服务器地址
   - 验证SSL/TLS设置

2. **注册请求失败**
   - 检查网络连接
   - 查看日志文件中的错误信息
   - 尝试更新请求头和参数

3. **验证码接收失败**
   - 检查邮箱垃圾邮件文件夹
   - 确认发件人过滤设置
   - 增加等待时间

### 日志文件

- `fifa_registration.log`: 注册操作日志
- `fifa_protocol_analysis.log`: 协议分析日志
- `fifa_api_analysis.log`: API拦截日志

## 更新日志

### v1.0.0 (2025-07-30)
- 初始版本发布
- 支持基本的FIFA账号注册
- 集成邮箱验证功能
- 提供协议分析工具

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和服务条款。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 提交Pull Request

---

**免责声明**: 本工具仅用于技术研究和学习目的，使用者需自行承担使用风险，并遵守相关服务条款。
