#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIFA自动注册机器人
基于协议逆向的高效FIFA账号注册工具
"""

import requests
import json
import time
import random
import string
import logging
from typing import Dict, List, Optional, Tuple
import re
from urllib.parse import urljoin, urlparse
import hashlib
import uuid
from email_manager import EmailManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fifa_registration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FIFARegistrationBot:
    """FIFA注册机器人"""
    
    def __init__(self, email_manager: EmailManager):
        """初始化注册机器人"""
        self.email_manager = email_manager
        self.session = requests.Session()
        self.base_url = "https://www.fifa.com"
        self.api_base = "https://cxm-api.fifa.com/fifaplusweb/api"
        self.auth_base = "https://auth.fifa.com"
        
        # 存储会话信息
        self.session_data = {}
        self.csrf_token = None
        self.device_id = None
        
        self.setup_session()
        
    def setup_session(self):
        """配置请求会话"""
        # 设置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })

        # 生成设备ID
        self.device_id = str(uuid.uuid4())
        
    def generate_random_user_data(self) -> Dict:
        """生成随机用户数据"""
        first_names = ['John', 'Jane', 'Mike', 'Sarah', 'David', 'Emma', 'Chris', 'Lisa', 'Tom', 'Anna']
        last_names = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez']
        
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        
        # 生成随机密码
        password = self.generate_strong_password()
        
        return {
            'first_name': first_name,
            'last_name': last_name,
            'password': password,
            'birth_year': random.randint(1980, 2000),
            'birth_month': random.randint(1, 12),
            'birth_day': random.randint(1, 28)
        }
    
    def generate_strong_password(self, length: int = 12) -> str:
        """生成强密码"""
        # 确保包含大小写字母、数字和特殊字符
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*"
        
        # 至少包含每种类型的一个字符
        password = [
            random.choice(lowercase),
            random.choice(uppercase),
            random.choice(digits),
            random.choice(special)
        ]
        
        # 填充剩余长度
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(random.choice(all_chars))
        
        # 打乱顺序
        random.shuffle(password)
        return ''.join(password)
    
    def initialize_session(self) -> bool:
        """初始化会话"""
        logger.info("初始化FIFA会话...")

        try:
            # 1. 尝试多个入口点
            entry_points = [
                self.base_url,
                "https://www.fifa.com/",
                "https://fifa.com/",
                "https://www.fifa.com/en"
            ]

            main_page_success = False
            for url in entry_points:
                try:
                    logger.info(f"尝试访问: {url}")
                    response = self.session.get(url, timeout=15, allow_redirects=True)
                    if response.status_code == 200:
                        logger.info(f"主页访问成功: {url}")
                        self.base_url = url.rstrip('/')
                        main_page_success = True
                        break
                    else:
                        logger.warning(f"访问失败 {url}: {response.status_code}")
                except Exception as e:
                    logger.warning(f"访问异常 {url}: {e}")
                    continue

            if not main_page_success:
                logger.error("所有入口点访问失败")
                return False

            # 2. 尝试访问注册页面
            register_urls = [
                f"{self.base_url}/account/register",
                f"{self.base_url}/register",
                f"{self.base_url}/signup",
                f"{self.base_url}/en/account/register"
            ]

            register_success = False
            for url in register_urls:
                try:
                    logger.info(f"尝试访问注册页面: {url}")
                    response = self.session.get(url, timeout=15, allow_redirects=True)
                    if response.status_code == 200:
                        logger.info(f"注册页面访问成功: {url}")
                        register_success = True
                        break
                    else:
                        logger.warning(f"注册页面访问失败 {url}: {response.status_code}")
                except Exception as e:
                    logger.warning(f"注册页面访问异常 {url}: {e}")
                    continue

            if not register_success:
                logger.warning("注册页面访问失败，但继续尝试")

            # 3. 尝试获取配置信息
            self.get_app_config()

            # 4. 初始化认证会话
            self.initialize_auth_session()

            return True

        except Exception as e:
            logger.error(f"初始化会话失败: {e}")
            return False
    
    def get_app_config(self):
        """获取应用配置"""
        try:
            # 尝试获取主题配置
            theme_url = f"{self.api_base}/themePalette"
            response = self.session.get(theme_url, timeout=10)
            if response.status_code == 200:
                logger.info("获取主题配置成功")
            
            # 尝试获取其他配置
            config_endpoints = [
                "/config",
                "/settings",
                "/init"
            ]
            
            for endpoint in config_endpoints:
                try:
                    url = f"{self.api_base}{endpoint}"
                    response = self.session.get(url, timeout=5)
                    if response.status_code == 200:
                        logger.info(f"获取配置成功: {endpoint}")
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.debug(f"获取应用配置失败: {e}")
    
    def initialize_auth_session(self):
        """初始化认证会话"""
        try:
            # 基于页面源码中的配置信息
            auth_config = {
                'client_id': '35072598-fc20-4142-a469-1b940db47e6f',
                'redirect_uri': 'https://www.fifa.com/auth',
                'response_type': 'code',
                'scope': 'openid profile email'
            }
            
            # 构建认证URL
            auth_url = f"{self.auth_base}/oauth2/authorize"
            params = {
                'client_id': auth_config['client_id'],
                'redirect_uri': auth_config['redirect_uri'],
                'response_type': auth_config['response_type'],
                'scope': auth_config['scope'],
                'state': str(uuid.uuid4())
            }
            
            # 访问认证端点
            response = self.session.get(auth_url, params=params, timeout=10)
            if response.status_code == 200:
                logger.info("认证会话初始化成功")
                
                # 尝试从响应中提取必要的token
                self.extract_auth_tokens(response.text)
            
        except Exception as e:
            logger.debug(f"初始化认证会话失败: {e}")
    
    def extract_auth_tokens(self, html_content: str):
        """从HTML中提取认证token"""
        try:
            # 查找CSRF token
            csrf_patterns = [
                r'csrf[_-]?token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'_token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'authenticity_token["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in csrf_patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    self.csrf_token = match.group(1)
                    logger.info("提取CSRF token成功")
                    break
            
            # 查找其他必要的token
            token_patterns = [
                r'access_token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'session_token["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'api_key["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in token_patterns:
                match = re.search(pattern, html_content, re.IGNORECASE)
                if match:
                    token_value = match.group(1)
                    logger.info(f"提取到token: {token_value[:10]}...")
                    break
                    
        except Exception as e:
            logger.debug(f"提取认证token失败: {e}")
    
    def register_account(self, email: str, user_data: Dict) -> Dict:
        """注册FIFA账号"""
        logger.info(f"开始注册FIFA账号: {email}")
        
        result = {
            'success': False,
            'email': email,
            'user_data': user_data,
            'error': None,
            'verification_needed': False
        }
        
        try:
            # 1. 初始化会话
            if not self.initialize_session():
                result['error'] = "会话初始化失败"
                return result
            
            # 2. 尝试不同的注册端点
            registration_endpoints = [
                f"{self.api_base}/auth/register",
                f"{self.api_base}/user/register",
                f"{self.api_base}/account/register",
                f"{self.auth_base}/register",
                f"{self.auth_base}/signup"
            ]
            
            registration_data = self.prepare_registration_data(email, user_data)
            
            for endpoint in registration_endpoints:
                logger.info(f"尝试注册端点: {endpoint}")
                
                response = self.attempt_registration(endpoint, registration_data)
                
                if response and response.get('success'):
                    result.update(response)
                    break
                elif response and response.get('verification_needed'):
                    result['verification_needed'] = True
                    result['verification_data'] = response.get('verification_data')
                    break
            
            # 3. 如果需要验证码，处理验证流程
            if result.get('verification_needed'):
                verification_result = self.handle_email_verification(email, result.get('verification_data'))
                result.update(verification_result)
            
        except Exception as e:
            logger.error(f"注册账号失败: {e}")
            result['error'] = str(e)
        
        return result
    
    def prepare_registration_data(self, email: str, user_data: Dict) -> Dict:
        """准备注册数据"""
        base_data = {
            'email': email,
            'password': user_data['password'],
            'firstName': user_data['first_name'],
            'lastName': user_data['last_name'],
            'dateOfBirth': f"{user_data['birth_year']}-{user_data['birth_month']:02d}-{user_data['birth_day']:02d}",
            'country': 'US',  # 默认美国
            'language': 'en',
            'acceptTerms': True,
            'acceptPrivacy': True,
            'marketingConsent': False
        }
        
        # 添加可能需要的额外字段
        extra_fields = {
            'deviceId': self.device_id,
            'clientId': '35072598-fc20-4142-a469-1b940db47e6f',
            'source': 'web',
            'platform': 'web'
        }
        
        if self.csrf_token:
            extra_fields['_token'] = self.csrf_token
            extra_fields['csrf_token'] = self.csrf_token
        
        # 尝试不同的数据格式
        variations = [
            {**base_data, **extra_fields},  # 完整数据
            base_data,  # 基础数据
            {  # 简化格式
                'username': email,
                'email': email,
                'password': user_data['password'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name']
            }
        ]
        
        return variations
    
    def attempt_registration(self, endpoint: str, registration_data_variations: List[Dict]) -> Optional[Dict]:
        """尝试注册"""
        headers = {
            'Content-Type': 'application/json',
            'Referer': f"{self.base_url}/account/register",
            'Origin': self.base_url
        }
        
        if self.csrf_token:
            headers['X-CSRF-Token'] = self.csrf_token
            headers['X-Requested-With'] = 'XMLHttpRequest'
        
        for i, data in enumerate(registration_data_variations):
            try:
                logger.info(f"尝试数据格式 {i+1}/{len(registration_data_variations)}")
                
                response = self.session.post(
                    endpoint,
                    json=data,
                    headers=headers,
                    timeout=15
                )
                
                logger.info(f"响应状态码: {response.status_code}")
                
                if response.status_code in [200, 201]:
                    try:
                        response_data = response.json()
                        logger.info("注册请求成功")
                        return {
                            'success': True,
                            'response_data': response_data,
                            'endpoint': endpoint
                        }
                    except:
                        # 可能是HTML响应，表示成功
                        if 'success' in response.text.lower() or 'verify' in response.text.lower():
                            return {
                                'success': True,
                                'verification_needed': True,
                                'endpoint': endpoint
                            }
                
                elif response.status_code == 400:
                    # 可能需要验证码或其他验证
                    try:
                        error_data = response.json()
                        if 'verify' in str(error_data).lower() or 'verification' in str(error_data).lower():
                            return {
                                'success': False,
                                'verification_needed': True,
                                'verification_data': error_data,
                                'endpoint': endpoint
                            }
                    except:
                        pass
                
                elif response.status_code == 422:
                    # 验证错误，尝试下一个格式
                    logger.info("数据格式验证失败，尝试下一个格式")
                    continue
                
                else:
                    logger.warning(f"注册失败，状态码: {response.status_code}")
                    logger.debug(f"响应内容: {response.text[:500]}")
                
            except Exception as e:
                logger.warning(f"注册请求异常: {e}")
                continue
        
        return None
    
    def handle_email_verification(self, email: str, verification_data: Dict) -> Dict:
        """处理邮箱验证"""
        logger.info(f"处理邮箱验证: {email}")
        
        result = {
            'success': False,
            'verified': False
        }
        
        try:
            # 等待验证邮件
            logger.info("等待验证邮件...")
            verification_code = self.email_manager.get_verification_code(
                email, 
                sender_filter="fifa",
                timeout=300,  # 5分钟超时
                check_interval=10
            )
            
            if verification_code:
                logger.info(f"收到验证码: {verification_code}")
                
                # 尝试验证
                verify_result = self.verify_email_code(email, verification_code, verification_data)
                result.update(verify_result)
            else:
                logger.warning("未收到验证邮件")
                result['error'] = "未收到验证邮件"
                
        except Exception as e:
            logger.error(f"邮箱验证失败: {e}")
            result['error'] = str(e)
        
        return result
    
    def verify_email_code(self, email: str, code: str, verification_data: Dict) -> Dict:
        """验证邮箱验证码"""
        logger.info(f"验证邮箱验证码: {email}")
        
        # 尝试不同的验证端点
        verify_endpoints = [
            f"{self.api_base}/auth/verify",
            f"{self.api_base}/user/verify",
            f"{self.api_base}/account/verify",
            f"{self.auth_base}/verify"
        ]
        
        verify_data_variations = [
            {
                'email': email,
                'code': code,
                'verificationCode': code
            },
            {
                'username': email,
                'token': code
            },
            {
                'email': email,
                'otp': code
            }
        ]
        
        headers = {
            'Content-Type': 'application/json',
            'Referer': f"{self.base_url}/account/register",
            'Origin': self.base_url
        }
        
        for endpoint in verify_endpoints:
            for data in verify_data_variations:
                try:
                    response = self.session.post(
                        endpoint,
                        json=data,
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code in [200, 201]:
                        logger.info("邮箱验证成功")
                        return {
                            'success': True,
                            'verified': True,
                            'endpoint': endpoint
                        }
                        
                except Exception as e:
                    logger.debug(f"验证请求失败: {e}")
                    continue
        
        return {
            'success': False,
            'verified': False,
            'error': "验证码验证失败"
        }
    
    def batch_register(self, email_list: List[str], count: int = None) -> List[Dict]:
        """批量注册账号"""
        if count:
            email_list = email_list[:count]
        
        logger.info(f"开始批量注册 {len(email_list)} 个账号")
        
        results = []
        
        for i, email in enumerate(email_list, 1):
            logger.info(f"注册进度: {i}/{len(email_list)}")
            
            # 生成随机用户数据
            user_data = self.generate_random_user_data()
            
            # 注册账号
            result = self.register_account(email, user_data)
            results.append(result)
            
            # 记录结果
            if result['success']:
                logger.info(f"✅ 注册成功: {email}")
            else:
                logger.warning(f"❌ 注册失败: {email} - {result.get('error', '未知错误')}")
            
            # 随机延迟，避免被检测
            if i < len(email_list):
                delay = random.uniform(5, 15)
                logger.info(f"等待 {delay:.1f} 秒...")
                time.sleep(delay)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        logger.info(f"批量注册完成: {success_count}/{len(results)} 成功")
        
        return results
    
    def save_results(self, results: List[Dict], filename: str = "fifa_registration_results.json"):
        """保存注册结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            logger.info(f"结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")

def main():
    """主函数"""
    # 初始化邮箱管理器
    email_manager = EmailManager()
    
    # 初始化注册机器人
    bot = FIFARegistrationBot(email_manager)
    
    # 示例：注册单个账号
    test_email = "<EMAIL>"  # 替换为实际邮箱
    user_data = bot.generate_random_user_data()
    
    result = bot.register_account(test_email, user_data)
    
    print("\n" + "="*50)
    print("FIFA注册结果")
    print("="*50)
    print(f"邮箱: {result['email']}")
    print(f"状态: {'成功' if result['success'] else '失败'}")
    if result.get('error'):
        print(f"错误: {result['error']}")
    
    # 保存结果
    bot.save_results([result])

if __name__ == "__main__":
    main()
