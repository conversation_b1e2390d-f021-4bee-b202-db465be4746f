#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速邮箱测试工具
验证邮箱配置是否正确
"""

import json
import logging
from email_manager import EmailManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_email_config():
    """测试邮箱配置"""
    print("="*50)
    print("FIFA邮箱系统快速测试")
    print("="*50)
    
    try:
        # 检查配置文件
        with open('email_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        emails = config.get('emails', [])
        print(f"发现 {len(emails)} 个邮箱配置")
        
        if not emails:
            print("❌ 未找到邮箱配置")
            print("请先编辑 email_config.json 文件添加您的邮箱信息")
            return
        
        # 显示配置信息
        print("\n📧 邮箱配置列表:")
        for i, email_config in enumerate(emails, 1):
            email = email_config.get('email', 'unknown')
            server = email_config.get('imap_server', 'unknown')
            port = email_config.get('imap_port', 'unknown')
            ssl = email_config.get('use_ssl', False)
            
            print(f"{i}. {email}")
            print(f"   服务器: {server}:{port}")
            print(f"   SSL: {'是' if ssl else '否'}")
            print()
        
        # 初始化邮箱管理器
        print("🔄 初始化邮箱管理器...")
        email_manager = EmailManager()
        
        print("✅ 邮箱管理器初始化成功")
        print(f"✅ 已加载 {len(email_manager.email_configs)} 个邮箱配置")
        
        # 显示可用邮箱
        print("\n📋 可用邮箱列表:")
        for email in email_manager.email_configs:
            print(f"  - {email}")
        
        print("\n" + "="*50)
        print("✅ 邮箱系统测试完成")
        print("💡 提示: 要测试实际连接，请确保邮箱配置信息正确")
        print("💡 提示: 可以运行 test_email_receiver.py 进行详细测试")
        print("="*50)
        
    except FileNotFoundError:
        print("❌ 未找到邮箱配置文件: email_config.json")
        print("请先运行 test_email_receiver.py 生成配置文件")
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        print("请检查 email_config.json 文件格式")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_email_config()
