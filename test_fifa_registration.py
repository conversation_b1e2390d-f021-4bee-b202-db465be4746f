#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIFA注册测试脚本
测试FIFA自动注册功能
"""

import json
import logging
from fifa_registration_bot import FIFARegistrationBot
from email_manager import EmailManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_single_registration():
    """测试单个账号注册"""
    print("="*60)
    print("FIFA单账号注册测试")
    print("="*60)
    
    try:
        # 初始化邮箱管理器
        email_manager = EmailManager()
        
        # 初始化注册机器人
        bot = FIFARegistrationBot(email_manager)
        
        # 生成测试数据
        test_email = "<EMAIL>"  # 请替换为实际的域名邮箱
        user_data = bot.generate_random_user_data()
        
        print(f"测试邮箱: {test_email}")
        print(f"用户数据: {user_data['first_name']} {user_data['last_name']}")
        print(f"密码: {user_data['password']}")
        print("-" * 60)
        
        # 执行注册
        result = bot.register_account(test_email, user_data)
        
        # 显示结果
        print("\n注册结果:")
        print(f"状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"邮箱: {result['email']}")
        
        if result.get('error'):
            print(f"错误信息: {result['error']}")
        
        if result.get('verification_needed'):
            print("需要邮箱验证")
        
        # 保存详细结果
        with open('test_registration_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n详细结果已保存到: test_registration_result.json")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        print(f"❌ 测试失败: {e}")

def test_batch_registration():
    """测试批量注册"""
    print("="*60)
    print("FIFA批量注册测试")
    print("="*60)
    
    try:
        # 初始化邮箱管理器
        email_manager = EmailManager()
        
        # 初始化注册机器人
        bot = FIFARegistrationBot(email_manager)
        
        # 测试邮箱列表（请替换为实际的域名邮箱）
        test_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        print(f"测试邮箱数量: {len(test_emails)}")
        print("邮箱列表:")
        for email in test_emails:
            print(f"  - {email}")
        print("-" * 60)
        
        # 执行批量注册
        results = bot.batch_register(test_emails)
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        failed_count = len(results) - success_count
        
        print(f"\n批量注册完成:")
        print(f"总数: {len(results)}")
        print(f"成功: {success_count}")
        print(f"失败: {failed_count}")
        print(f"成功率: {success_count/len(results)*100:.1f}%")
        
        # 显示详细结果
        print("\n详细结果:")
        for i, result in enumerate(results, 1):
            status = "✅" if result['success'] else "❌"
            error_info = f" ({result.get('error', '')})" if result.get('error') else ""
            print(f"{i}. {status} {result['email']}{error_info}")
        
        # 保存结果
        bot.save_results(results, 'batch_registration_results.json')
        print(f"\n详细结果已保存到: batch_registration_results.json")
        
    except Exception as e:
        logger.error(f"批量测试失败: {e}")
        print(f"❌ 批量测试失败: {e}")

def test_email_connection():
    """测试邮箱连接"""
    print("="*60)
    print("邮箱连接测试")
    print("="*60)
    
    try:
        email_manager = EmailManager()
        
        # 检查配置文件
        try:
            with open('email_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"找到 {len(config.get('email_accounts', []))} 个邮箱配置")
            
            # 测试每个邮箱连接
            for i, account in enumerate(config.get('email_accounts', []), 1):
                email = account.get('email', 'unknown')
                print(f"\n{i}. 测试邮箱: {email}")
                
                try:
                    # 这里可以添加具体的连接测试逻辑
                    print(f"   服务器: {account.get('imap_server', 'unknown')}")
                    print(f"   端口: {account.get('imap_port', 'unknown')}")
                    print(f"   SSL: {account.get('use_ssl', False)}")
                    print("   状态: ✅ 配置正常")
                except Exception as e:
                    print(f"   状态: ❌ 配置错误 - {e}")
        
        except FileNotFoundError:
            print("❌ 未找到邮箱配置文件 email_config.json")
            print("请先运行 test_email_receiver.py 生成配置文件")
        
    except Exception as e:
        logger.error(f"邮箱连接测试失败: {e}")
        print(f"❌ 邮箱连接测试失败: {e}")

def main():
    """主菜单"""
    while True:
        print("\n" + "="*60)
        print("FIFA自动注册测试工具")
        print("="*60)
        print("1. 测试邮箱连接")
        print("2. 单账号注册测试")
        print("3. 批量注册测试")
        print("4. 退出")
        print("-" * 60)
        
        choice = input("请选择操作 (1-4): ").strip()
        
        if choice == '1':
            test_email_connection()
        elif choice == '2':
            test_single_registration()
        elif choice == '3':
            test_batch_registration()
        elif choice == '4':
            print("退出程序")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
