#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试FIFA注册功能
"""

from fifa_registration_bot import FIFARegistrationBot
from email_manager import EmailManager
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    print("="*50)
    print("FIFA注册直接测试")
    print("="*50)
    
    try:
        # 初始化邮箱管理器
        print("🔄 初始化邮箱管理器...")
        email_manager = EmailManager()
        print(f"✅ 已加载 {len(email_manager.email_configs)} 个邮箱配置")
        
        # 初始化注册机器人
        print("🔄 初始化FIFA注册机器人...")
        bot = FIFARegistrationBot(email_manager)
        print("✅ 注册机器人初始化成功")
        
        # 使用真实邮箱进行测试
        test_email = "<EMAIL>"
        print(f"📧 使用邮箱: {test_email}")
        
        # 执行注册
        print("🚀 开始注册测试...")

        # 生成用户数据
        user_data = bot.generate_random_user_data()
        print(f"生成用户数据: {user_data['first_name']} {user_data['last_name']}")

        result = bot.register_account(test_email, user_data)
        
        # 显示结果
        print("\n" + "="*50)
        print("注册测试结果")
        print("="*50)
        print(f"邮箱: {test_email}")
        print(f"状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
        
        if result['success']:
            print(f"用户名: {result.get('user_data', {}).get('first_name', 'N/A')} {result.get('user_data', {}).get('last_name', 'N/A')}")
            print(f"密码: {result.get('user_data', {}).get('password', 'N/A')}")
        else:
            print(f"错误信息: {result.get('error', 'Unknown error')}")
        
        print("="*50)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.exception("测试异常")

if __name__ == "__main__":
    main()
