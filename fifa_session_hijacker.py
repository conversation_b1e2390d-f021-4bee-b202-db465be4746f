#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIFA会话劫持分析器
通过拦截真实用户操作获取完整的API流程
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional
import websockets
import threading
from datetime import datetime
import re
import base64
import gzip
from urllib.parse import urlparse, parse_qs

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fifa_session_hijack.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FIFASessionHijacker:
    """FIFA会话劫持分析器"""
    
    def __init__(self):
        """初始化会话劫持器"""
        self.captured_requests = []
        self.captured_responses = []
        self.session_tokens = {}
        self.api_endpoints = set()
        self.form_data = {}
        self.cookies = {}
        self.headers_patterns = {}
        
        # 分析结果
        self.analysis_result = {
            'registration_flow': [],
            'api_endpoints': [],
            'required_headers': {},
            'session_management': {},
            'form_fields': {},
            'verification_flow': [],
            'csrf_tokens': [],
            'authentication': {}
        }
        
    def start_proxy_server(self, port: int = 8888):
        """启动代理服务器"""
        logger.info(f"启动代理服务器，端口: {port}")
        
        # 这里可以集成mitmproxy或其他代理工具
        # 由于复杂性，我们使用更简单的方法
        self.setup_browser_debugging()
        
    def setup_browser_debugging(self):
        """设置浏览器调试"""
        logger.info("设置浏览器调试模式...")
        
        instructions = """
        
        ==========================================
        FIFA会话劫持分析 - 手动操作指南
        ==========================================
        
        请按照以下步骤操作：
        
        1. 打开Chrome浏览器，启动调试模式：
           chrome.exe --remote-debugging-port=9222 --user-data-dir=temp
        
        2. 访问 FIFA 注册页面：
           https://www.fifa.com/account/register
        
        3. 打开开发者工具 (F12)，切换到 Network 标签页
        
        4. 开始注册流程：
           - 填写注册表单
           - 点击注册按钮
           - 完成邮箱验证
        
        5. 在开发者工具中：
           - 右键点击相关的网络请求
           - 选择 "Copy as cURL"
           - 将结果粘贴到 captured_requests.txt 文件中
        
        6. 运行分析：
           python fifa_session_hijacker.py --analyze
        
        ==========================================
        
        """
        
        print(instructions)
        
        # 创建捕获文件
        with open('captured_requests.txt', 'w', encoding='utf-8') as f:
            f.write("# 请将从浏览器开发者工具复制的cURL命令粘贴到这里\n")
            f.write("# 每个请求占一行，以 curl 开头\n\n")
        
        # 启动文件监控
        self.monitor_capture_file()
        
    def monitor_capture_file(self):
        """监控捕获文件"""
        logger.info("开始监控捕获文件...")
        
        def monitor():
            last_size = 0
            while True:
                try:
                    with open('captured_requests.txt', 'r', encoding='utf-8') as f:
                        content = f.read()
                        current_size = len(content)
                        
                        if current_size > last_size:
                            logger.info("检测到新的捕获数据")
                            self.parse_captured_data(content)
                            last_size = current_size
                            
                except FileNotFoundError:
                    pass
                except Exception as e:
                    logger.error(f"监控文件异常: {e}")
                
                time.sleep(2)
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()
        
    def parse_captured_data(self, content: str):
        """解析捕获的数据"""
        logger.info("解析捕获的数据...")
        
        curl_commands = []
        lines = content.split('\n')
        
        current_curl = ""
        for line in lines:
            line = line.strip()
            if line.startswith('curl'):
                if current_curl:
                    curl_commands.append(current_curl)
                current_curl = line
            elif line and not line.startswith('#') and current_curl:
                current_curl += " " + line
        
        if current_curl:
            curl_commands.append(current_curl)
        
        # 分析每个cURL命令
        for curl_cmd in curl_commands:
            self.analyze_curl_command(curl_cmd)
        
        # 生成分析报告
        self.generate_analysis_report()
        
    def analyze_curl_command(self, curl_cmd: str):
        """分析cURL命令"""
        try:
            # 提取URL
            url_match = re.search(r"curl\s+['\"]([^'\"]+)['\"]", curl_cmd)
            if not url_match:
                url_match = re.search(r"curl\s+([^\s]+)", curl_cmd)
            
            if not url_match:
                return
            
            url = url_match.group(1)
            parsed_url = urlparse(url)
            
            # 检查是否是FIFA相关的请求
            if 'fifa.com' not in parsed_url.netloc:
                return
            
            logger.info(f"分析FIFA请求: {url}")
            
            # 提取请求方法
            method = 'GET'
            if '-X POST' in curl_cmd or '--data' in curl_cmd:
                method = 'POST'
            elif '-X PUT' in curl_cmd:
                method = 'PUT'
            elif '-X DELETE' in curl_cmd:
                method = 'DELETE'
            
            # 提取请求头
            headers = {}
            header_matches = re.findall(r"-H\s+['\"]([^:]+):\s*([^'\"]+)['\"]", curl_cmd)
            for header_name, header_value in header_matches:
                headers[header_name] = header_value
            
            # 提取请求数据
            data = None
            data_match = re.search(r"--data-raw\s+['\"]([^'\"]+)['\"]", curl_cmd)
            if not data_match:
                data_match = re.search(r"--data\s+['\"]([^'\"]+)['\"]", curl_cmd)
            
            if data_match:
                data = data_match.group(1)
                try:
                    # 尝试解析JSON数据
                    data = json.loads(data)
                except:
                    pass
            
            # 存储请求信息
            request_info = {
                'timestamp': datetime.now().isoformat(),
                'method': method,
                'url': url,
                'headers': headers,
                'data': data,
                'curl_command': curl_cmd
            }
            
            self.captured_requests.append(request_info)
            
            # 分析请求类型
            self.classify_request(request_info)
            
        except Exception as e:
            logger.error(f"分析cURL命令失败: {e}")
    
    def classify_request(self, request_info: Dict):
        """分类请求类型"""
        url = request_info['url'].lower()
        method = request_info['method']
        
        # 注册相关请求
        if any(keyword in url for keyword in ['register', 'signup', 'create', 'account']):
            if method == 'POST':
                self.analysis_result['registration_flow'].append(request_info)
                logger.info("发现注册请求")
        
        # 验证相关请求
        elif any(keyword in url for keyword in ['verify', 'confirm', 'validate', 'otp']):
            self.analysis_result['verification_flow'].append(request_info)
            logger.info("发现验证请求")
        
        # API端点
        if any(keyword in url for keyword in ['api', 'ajax', 'json']):
            self.analysis_result['api_endpoints'].append(request_info)
            logger.info(f"发现API端点: {request_info['url']}")
        
        # 提取CSRF token
        headers = request_info.get('headers', {})
        for header_name, header_value in headers.items():
            if 'csrf' in header_name.lower() or 'token' in header_name.lower():
                self.analysis_result['csrf_tokens'].append({
                    'header': header_name,
                    'value': header_value,
                    'url': request_info['url']
                })
        
        # 提取表单字段
        data = request_info.get('data')
        if isinstance(data, dict):
            for field_name, field_value in data.items():
                if field_name not in self.analysis_result['form_fields']:
                    self.analysis_result['form_fields'][field_name] = []
                self.analysis_result['form_fields'][field_name].append({
                    'value': field_value,
                    'url': request_info['url'],
                    'timestamp': request_info['timestamp']
                })
    
    def generate_analysis_report(self):
        """生成分析报告"""
        logger.info("生成分析报告...")
        
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_requests': len(self.captured_requests),
            'registration_requests': len(self.analysis_result['registration_flow']),
            'verification_requests': len(self.analysis_result['verification_flow']),
            'api_endpoints': len(self.analysis_result['api_endpoints']),
            'csrf_tokens': len(self.analysis_result['csrf_tokens']),
            'form_fields': len(self.analysis_result['form_fields']),
            'detailed_analysis': self.analysis_result,
            'captured_requests': self.captured_requests
        }
        
        # 保存详细报告
        with open('fifa_session_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # 生成可执行的API调用代码
        self.generate_api_code()
        
        # 打印摘要
        self.print_analysis_summary(report)
    
    def generate_api_code(self):
        """生成可执行的API调用代码"""
        logger.info("生成API调用代码...")
        
        code_template = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于会话劫持分析的FIFA注册API调用
自动生成于: {timestamp}
"""

import requests
import json
import time
from typing import Dict, Optional

class FIFARegistrationAPI:
    """基于真实会话分析的FIFA注册API"""
    
    def __init__(self):
        self.session = requests.Session()
        self.setup_session()
    
    def setup_session(self):
        """设置会话"""
        # 基于捕获的请求头设置
        self.session.headers.update({{
{headers}
        }})
    
{methods}

def main():
    """测试API调用"""
    api = FIFARegistrationAPI()
    
    # 示例调用
    test_data = {{
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "firstName": "Test",
        "lastName": "User"
    }}
    
    result = api.register_account(test_data)
    print(f"注册结果: {{result}}")

if __name__ == "__main__":
    main()
'''
        
        # 生成请求头
        common_headers = {}
        for req in self.captured_requests:
            for header_name, header_value in req.get('headers', {}).items():
                if header_name.lower() not in ['content-length', 'cookie']:
                    common_headers[header_name] = header_value
        
        headers_code = ""
        for header_name, header_value in common_headers.items():
            headers_code += f'            "{header_name}": "{header_value}",\n'
        
        # 生成方法
        methods_code = ""
        
        # 注册方法
        if self.analysis_result['registration_flow']:
            reg_req = self.analysis_result['registration_flow'][0]
            methods_code += f'''
    def register_account(self, user_data: Dict) -> Dict:
        """注册账号"""
        url = "{reg_req['url']}"
        
        # 基于捕获的数据结构
        data = user_data
        
        try:
            response = self.session.{reg_req['method'].lower()}(
                url,
                json=data,
                timeout=15
            )
            
            if response.status_code == 200:
                return {{"success": True, "data": response.json()}}
            else:
                return {{"success": False, "error": f"HTTP {{response.status_code}}"}}
                
        except Exception as e:
            return {{"success": False, "error": str(e)}}
'''
        
        # 验证方法
        if self.analysis_result['verification_flow']:
            ver_req = self.analysis_result['verification_flow'][0]
            methods_code += f'''
    def verify_email(self, email: str, code: str) -> Dict:
        """验证邮箱"""
        url = "{ver_req['url']}"
        
        data = {{
            "email": email,
            "code": code
        }}
        
        try:
            response = self.session.{ver_req['method'].lower()}(
                url,
                json=data,
                timeout=15
            )
            
            if response.status_code == 200:
                return {{"success": True, "data": response.json()}}
            else:
                return {{"success": False, "error": f"HTTP {{response.status_code}}"}}
                
        except Exception as e:
            return {{"success": False, "error": str(e)}}
'''
        
        # 生成完整代码
        generated_code = code_template.format(
            timestamp=datetime.now().isoformat(),
            headers=headers_code,
            methods=methods_code
        )
        
        # 保存生成的代码
        with open('fifa_api_generated.py', 'w', encoding='utf-8') as f:
            f.write(generated_code)
        
        logger.info("API调用代码已生成: fifa_api_generated.py")
    
    def print_analysis_summary(self, report: Dict):
        """打印分析摘要"""
        print("\n" + "="*60)
        print("FIFA会话劫持分析结果")
        print("="*60)
        print(f"总请求数: {report['total_requests']}")
        print(f"注册请求: {report['registration_requests']}")
        print(f"验证请求: {report['verification_requests']}")
        print(f"API端点: {report['api_endpoints']}")
        print(f"CSRF令牌: {report['csrf_tokens']}")
        print(f"表单字段: {report['form_fields']}")
        
        if self.analysis_result['registration_flow']:
            print("\n发现的注册端点:")
            for req in self.analysis_result['registration_flow']:
                print(f"  - {req['method']} {req['url']}")
        
        if self.analysis_result['verification_flow']:
            print("\n发现的验证端点:")
            for req in self.analysis_result['verification_flow']:
                print(f"  - {req['method']} {req['url']}")
        
        if self.analysis_result['form_fields']:
            print("\n发现的表单字段:")
            for field_name in self.analysis_result['form_fields']:
                print(f"  - {field_name}")
        
        print(f"\n详细报告已保存: fifa_session_analysis.json")
        print(f"生成的API代码: fifa_api_generated.py")
        print("="*60)

def main():
    """主函数"""
    hijacker = FIFASessionHijacker()
    
    print("FIFA会话劫持分析器启动")
    print("请按照提示进行手动操作...")
    
    try:
        # 保持程序运行，监控捕获文件
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n分析器已停止")

if __name__ == "__main__":
    main()
