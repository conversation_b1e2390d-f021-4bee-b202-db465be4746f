#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIFA API拦截器
使用浏览器自动化拦截和分析FIFA注册过程中的API调用
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fifa_api_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FIFAAPIInterceptor:
    """FIFA API拦截器"""
    
    def __init__(self):
        """初始化拦截器"""
        self.driver = None
        self.intercepted_requests = []
        self.intercepted_responses = []
        self.registration_flow = {}
        
    async def setup_driver(self):
        """设置无头浏览器"""
        logger.info("初始化浏览器...")
        
        options = webdriver.ChromeOptions()
        # 反检测设置
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户代理
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        self.driver = await webdriver.Chrome(options=options)
        
        # 执行反检测脚本
        await self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        logger.info("浏览器初始化完成")
    
    async def setup_network_interception(self):
        """设置网络拦截"""
        logger.info("设置网络拦截...")
        
        # 启用网络域
        await self.driver.execute_cdp_cmd('Network.enable', {})
        await self.driver.execute_cdp_cmd('Runtime.enable', {})
        
        # 设置请求拦截
        await self.driver.execute_cdp_cmd('Network.setRequestInterception', {
            'patterns': [{'urlPattern': '*', 'resourceType': 'XHR'},
                        {'urlPattern': '*', 'resourceType': 'Fetch'}]
        })
        
        # 添加事件监听器
        self.driver.add_cdp_listener('Network.requestWillBeSent', self.on_request)
        self.driver.add_cdp_listener('Network.responseReceived', self.on_response)
        self.driver.add_cdp_listener('Network.loadingFinished', self.on_loading_finished)
        
        logger.info("网络拦截设置完成")
    
    async def on_request(self, params):
        """请求拦截处理"""
        request = params.get('request', {})
        url = request.get('url', '')
        method = request.get('method', '')
        headers = request.get('headers', {})
        post_data = request.get('postData', '')
        
        # 过滤FIFA相关的API请求
        if any(keyword in url.lower() for keyword in ['fifa.com', 'register', 'signup', 'auth', 'api']):
            logger.info(f"拦截请求: {method} {url}")
            
            request_info = {
                'timestamp': time.time(),
                'request_id': params.get('requestId'),
                'url': url,
                'method': method,
                'headers': headers,
                'post_data': post_data,
                'type': 'request'
            }
            
            self.intercepted_requests.append(request_info)
            
            # 解析POST数据
            if post_data and method.upper() == 'POST':
                try:
                    if 'application/json' in headers.get('content-type', ''):
                        request_info['json_data'] = json.loads(post_data)
                    else:
                        request_info['form_data'] = self.parse_form_data(post_data)
                except Exception as e:
                    logger.warning(f"解析POST数据失败: {e}")
    
    async def on_response(self, params):
        """响应拦截处理"""
        response = params.get('response', {})
        url = response.get('url', '')
        status = response.get('status', 0)
        headers = response.get('headers', {})
        
        if any(keyword in url.lower() for keyword in ['fifa.com', 'register', 'signup', 'auth', 'api']):
            logger.info(f"拦截响应: {status} {url}")
            
            response_info = {
                'timestamp': time.time(),
                'request_id': params.get('requestId'),
                'url': url,
                'status': status,
                'headers': headers,
                'type': 'response'
            }
            
            self.intercepted_responses.append(response_info)
    
    async def on_loading_finished(self, params):
        """加载完成处理"""
        request_id = params.get('requestId')
        
        # 获取响应体
        try:
            response_body = await self.driver.execute_cdp_cmd('Network.getResponseBody', {
                'requestId': request_id
            })
            
            # 找到对应的响应并添加body
            for response in self.intercepted_responses:
                if response.get('request_id') == request_id:
                    body = response_body.get('body', '')
                    if response_body.get('base64Encoded'):
                        import base64
                        body = base64.b64decode(body).decode('utf-8', errors='ignore')
                    
                    response['body'] = body
                    
                    # 尝试解析JSON响应
                    if 'application/json' in response.get('headers', {}).get('content-type', ''):
                        try:
                            response['json_data'] = json.loads(body)
                        except:
                            pass
                    break
                    
        except Exception as e:
            logger.debug(f"获取响应体失败: {e}")
    
    def parse_form_data(self, post_data: str) -> Dict:
        """解析表单数据"""
        form_data = {}
        try:
            pairs = post_data.split('&')
            for pair in pairs:
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    form_data[key] = value
        except Exception as e:
            logger.warning(f"解析表单数据失败: {e}")
        
        return form_data
    
    async def analyze_registration_flow(self):
        """分析注册流程"""
        logger.info("开始分析FIFA注册流程...")
        
        try:
            await self.setup_driver()
            await self.setup_network_interception()
            
            # 访问注册页面
            logger.info("访问FIFA注册页面...")
            await self.driver.get('https://www.fifa.com/account/register', wait_load=True)
            
            # 等待页面加载
            await asyncio.sleep(5)
            
            # 尝试查找注册表单元素
            await self.find_registration_elements()
            
            # 模拟填写表单（如果找到）
            await self.simulate_registration_attempt()
            
            # 等待更多网络请求
            await asyncio.sleep(10)
            
            # 分析拦截到的数据
            self.analyze_intercepted_data()
            
        except Exception as e:
            logger.error(f"分析注册流程失败: {e}")
        finally:
            if self.driver:
                await self.driver.quit()
    
    async def find_registration_elements(self):
        """查找注册表单元素"""
        logger.info("查找注册表单元素...")
        
        try:
            # 等待React应用加载
            await asyncio.sleep(3)
            
            # 查找常见的注册表单元素
            selectors = [
                'input[type="email"]',
                'input[name*="email"]',
                'input[placeholder*="email"]',
                'input[type="password"]',
                'input[name*="password"]',
                'button[type="submit"]',
                'button[class*="register"]',
                'button[class*="signup"]',
                'form'
            ]
            
            found_elements = {}
            
            for selector in selectors:
                try:
                    elements = await self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        found_elements[selector] = len(elements)
                        logger.info(f"找到元素: {selector} ({len(elements)}个)")
                except Exception as e:
                    logger.debug(f"查找元素 {selector} 失败: {e}")
            
            self.registration_flow['found_elements'] = found_elements
            
            # 获取页面源码进行进一步分析
            page_source = await self.driver.page_source
            self.analyze_page_source(page_source)
            
        except Exception as e:
            logger.error(f"查找注册元素失败: {e}")
    
    def analyze_page_source(self, page_source: str):
        """分析页面源码"""
        logger.info("分析页面源码...")
        
        # 查找React组件和API端点
        api_patterns = [
            r'api["\']?\s*:\s*["\']([^"\']+)["\']',
            r'endpoint["\']?\s*:\s*["\']([^"\']+)["\']',
            r'url["\']?\s*:\s*["\']([^"\']*api[^"\']*)["\']',
            r'fetch\(["\']([^"\']*)["\']',
            r'axios\.[a-z]+\(["\']([^"\']*)["\']'
        ]
        
        found_apis = []
        for pattern in api_patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            found_apis.extend(matches)
        
        # 去重并过滤
        unique_apis = list(set([api for api in found_apis if api and len(api) > 5]))
        
        self.registration_flow['potential_apis'] = unique_apis
        logger.info(f"发现潜在API端点: {len(unique_apis)}个")
        
        for api in unique_apis[:10]:  # 只显示前10个
            logger.info(f"  - {api}")
    
    async def simulate_registration_attempt(self):
        """模拟注册尝试"""
        logger.info("模拟注册尝试...")
        
        try:
            # 尝试填写邮箱字段
            email_selectors = [
                'input[type="email"]',
                'input[name*="email"]',
                'input[placeholder*="email"]'
            ]
            
            for selector in email_selectors:
                try:
                    email_input = await self.driver.find_element(By.CSS_SELECTOR, selector)
                    if email_input:
                        await email_input.send_keys('<EMAIL>')
                        logger.info(f"填写邮箱字段: {selector}")
                        break
                except:
                    continue
            
            # 尝试填写密码字段
            password_selectors = [
                'input[type="password"]',
                'input[name*="password"]'
            ]
            
            for selector in password_selectors:
                try:
                    password_input = await self.driver.find_element(By.CSS_SELECTOR, selector)
                    if password_input:
                        await password_input.send_keys('TestPassword123!')
                        logger.info(f"填写密码字段: {selector}")
                        break
                except:
                    continue
            
            # 等待可能的API调用
            await asyncio.sleep(3)
            
            # 尝试点击提交按钮（但不实际提交）
            submit_selectors = [
                'button[type="submit"]',
                'button[class*="register"]',
                'button[class*="signup"]',
                'input[type="submit"]'
            ]
            
            for selector in submit_selectors:
                try:
                    submit_button = await self.driver.find_element(By.CSS_SELECTOR, selector)
                    if submit_button:
                        logger.info(f"找到提交按钮: {selector}")
                        # 不实际点击，只是记录
                        break
                except:
                    continue
                    
        except Exception as e:
            logger.error(f"模拟注册失败: {e}")
    
    def analyze_intercepted_data(self):
        """分析拦截到的数据"""
        logger.info("分析拦截到的网络数据...")
        
        # 分析请求
        api_requests = []
        for request in self.intercepted_requests:
            if any(keyword in request['url'].lower() for keyword in ['api', 'register', 'signup', 'auth']):
                api_requests.append(request)
        
        # 分析响应
        api_responses = []
        for response in self.intercepted_responses:
            if any(keyword in response['url'].lower() for keyword in ['api', 'register', 'signup', 'auth']):
                api_responses.append(response)
        
        self.registration_flow.update({
            'total_requests': len(self.intercepted_requests),
            'total_responses': len(self.intercepted_responses),
            'api_requests': api_requests,
            'api_responses': api_responses
        })
        
        logger.info(f"拦截到 {len(self.intercepted_requests)} 个请求")
        logger.info(f"拦截到 {len(self.intercepted_responses)} 个响应")
        logger.info(f"API相关请求: {len(api_requests)} 个")
        logger.info(f"API相关响应: {len(api_responses)} 个")
    
    def save_analysis_result(self, filename: str = "fifa_api_analysis.json"):
        """保存分析结果"""
        try:
            result = {
                'timestamp': time.time(),
                'registration_flow': self.registration_flow,
                'intercepted_requests': self.intercepted_requests,
                'intercepted_responses': self.intercepted_responses
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"分析结果已保存到: {filename}")
            
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")

async def main():
    """主函数"""
    interceptor = FIFAAPIInterceptor()
    
    try:
        await interceptor.analyze_registration_flow()
        interceptor.save_analysis_result()
        
        # 显示摘要
        print("\n" + "="*50)
        print("FIFA API拦截分析结果")
        print("="*50)
        
        flow = interceptor.registration_flow
        print(f"找到的表单元素: {len(flow.get('found_elements', {}))}")
        print(f"潜在API端点: {len(flow.get('potential_apis', []))}")
        print(f"拦截的请求: {flow.get('total_requests', 0)}")
        print(f"拦截的响应: {flow.get('total_responses', 0)}")
        print(f"API相关请求: {len(flow.get('api_requests', []))}")
        
        # 显示关键API端点
        if flow.get('api_requests'):
            print("\n关键API端点:")
            for req in flow['api_requests'][:5]:
                print(f"  {req['method']} {req['url']}")
        
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
