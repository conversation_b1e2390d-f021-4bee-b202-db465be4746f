#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FIFA协议逆向分析工具
分析FIFA网站的注册API接口，提取关键参数和流程
"""

import requests
import json
import re
import time
import logging
from urllib.parse import urljoin, urlparse, parse_qs
from typing import Dict, List, Optional, Tuple
import hashlib
import random
import string
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fifa_protocol_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FIFAProtocolAnalyzer:
    """FIFA协议分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.base_url = "https://www.fifa.com"
        self.session = requests.Session()
        self.setup_session()
        
        # 存储分析结果
        self.endpoints = {}
        self.headers_patterns = {}
        self.csrf_tokens = {}
        self.cookies = {}
        self.form_data_patterns = {}
        
    def setup_session(self):
        """配置请求会话"""
        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
    
    def analyze_registration_flow(self) -> Dict:
        """分析FIFA注册流程"""
        logger.info("开始分析FIFA注册流程...")
        
        analysis_result = {
            'registration_page': None,
            'api_endpoints': [],
            'required_fields': [],
            'csrf_protection': None,
            'captcha_info': None,
            'validation_rules': {},
            'success': False
        }
        
        try:
            # 第一步：访问注册页面
            registration_info = self.get_registration_page()
            if registration_info:
                analysis_result['registration_page'] = registration_info
                
                # 第二步：分析表单结构
                form_analysis = self.analyze_registration_form(registration_info['content'])
                analysis_result.update(form_analysis)
                
                # 第三步：检测API端点
                api_endpoints = self.detect_api_endpoints(registration_info['content'])
                analysis_result['api_endpoints'] = api_endpoints
                
                # 第四步：分析CSRF保护
                csrf_info = self.analyze_csrf_protection(registration_info['content'])
                analysis_result['csrf_protection'] = csrf_info
                
                # 第五步：检测验证码
                captcha_info = self.detect_captcha_system(registration_info['content'])
                analysis_result['captcha_info'] = captcha_info
                
                analysis_result['success'] = True
                logger.info("FIFA注册流程分析完成")
            
        except Exception as e:
            logger.error(f"分析FIFA注册流程时出错: {e}")
            analysis_result['error'] = str(e)
        
        return analysis_result
    
    def get_registration_page(self) -> Optional[Dict]:
        """获取注册页面"""
        logger.info("正在获取FIFA注册页面...")
        
        # 常见的注册页面路径
        registration_paths = [
            "/account/register",
            "/register",
            "/signup",
            "/account/signup",
            "/user/register",
            "/auth/register"
        ]
        
        for path in registration_paths:
            try:
                url = urljoin(self.base_url, path)
                logger.info(f"尝试访问: {url}")
                
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    logger.info(f"成功访问注册页面: {url}")
                    return {
                        'url': url,
                        'status_code': response.status_code,
                        'headers': dict(response.headers),
                        'cookies': dict(response.cookies),
                        'content': response.text,
                        'content_length': len(response.text)
                    }
                else:
                    logger.info(f"页面返回状态码: {response.status_code}")
                    
            except Exception as e:
                logger.warning(f"访问 {path} 失败: {e}")
                continue
        
        # 尝试从主页查找注册链接
        logger.info("尝试从主页查找注册链接...")
        return self.find_registration_from_homepage()
    
    def find_registration_from_homepage(self) -> Optional[Dict]:
        """从主页查找注册链接"""
        try:
            response = self.session.get(self.base_url, timeout=10)
            if response.status_code == 200:
                # 查找注册相关的链接
                registration_patterns = [
                    r'href=["\']([^"\']*register[^"\']*)["\']',
                    r'href=["\']([^"\']*signup[^"\']*)["\']',
                    r'href=["\']([^"\']*account[^"\']*create[^"\']*)["\']',
                ]
                
                for pattern in registration_patterns:
                    matches = re.findall(pattern, response.text, re.IGNORECASE)
                    for match in matches:
                        if match.startswith('http'):
                            registration_url = match
                        else:
                            registration_url = urljoin(self.base_url, match)
                        
                        logger.info(f"找到注册链接: {registration_url}")
                        
                        # 尝试访问找到的注册链接
                        try:
                            reg_response = self.session.get(registration_url, timeout=10)
                            if reg_response.status_code == 200:
                                return {
                                    'url': registration_url,
                                    'status_code': reg_response.status_code,
                                    'headers': dict(reg_response.headers),
                                    'cookies': dict(reg_response.cookies),
                                    'content': reg_response.text,
                                    'content_length': len(reg_response.text)
                                }
                        except Exception as e:
                            logger.warning(f"访问注册链接失败: {e}")
                            continue
                            
        except Exception as e:
            logger.error(f"从主页查找注册链接失败: {e}")
        
        return None
    
    def analyze_registration_form(self, html_content: str) -> Dict:
        """分析注册表单结构"""
        logger.info("分析注册表单结构...")
        
        result = {
            'required_fields': [],
            'optional_fields': [],
            'validation_rules': {},
            'form_action': None,
            'form_method': 'POST'
        }
        
        try:
            # 查找表单
            form_pattern = r'<form[^>]*>(.*?)</form>'
            forms = re.findall(form_pattern, html_content, re.DOTALL | re.IGNORECASE)
            
            for form in forms:
                # 检查是否是注册表单
                if any(keyword in form.lower() for keyword in ['register', 'signup', 'create', 'account']):
                    logger.info("找到注册表单")
                    
                    # 提取表单action
                    action_match = re.search(r'action=["\']([^"\']*)["\']', form, re.IGNORECASE)
                    if action_match:
                        result['form_action'] = action_match.group(1)
                    
                    # 提取表单method
                    method_match = re.search(r'method=["\']([^"\']*)["\']', form, re.IGNORECASE)
                    if method_match:
                        result['form_method'] = method_match.group(1).upper()
                    
                    # 分析输入字段
                    input_pattern = r'<input[^>]*>'
                    inputs = re.findall(input_pattern, form, re.IGNORECASE)
                    
                    for input_tag in inputs:
                        field_info = self.parse_input_field(input_tag)
                        if field_info:
                            if field_info.get('required'):
                                result['required_fields'].append(field_info)
                            else:
                                result['optional_fields'].append(field_info)
                            
                            # 提取验证规则
                            if field_info.get('pattern') or field_info.get('minlength') or field_info.get('maxlength'):
                                result['validation_rules'][field_info['name']] = {
                                    'pattern': field_info.get('pattern'),
                                    'minlength': field_info.get('minlength'),
                                    'maxlength': field_info.get('maxlength'),
                                    'type': field_info.get('type')
                                }
                    
                    break
            
            logger.info(f"找到 {len(result['required_fields'])} 个必填字段")
            logger.info(f"找到 {len(result['optional_fields'])} 个可选字段")
            
        except Exception as e:
            logger.error(f"分析注册表单失败: {e}")
        
        return result
    
    def parse_input_field(self, input_tag: str) -> Optional[Dict]:
        """解析输入字段"""
        try:
            field_info = {}
            
            # 提取基本属性
            attributes = ['name', 'type', 'id', 'class', 'placeholder', 'value']
            for attr in attributes:
                pattern = f'{attr}=["\']([^"\']*)["\']'
                match = re.search(pattern, input_tag, re.IGNORECASE)
                if match:
                    field_info[attr] = match.group(1)
            
            # 检查是否必填
            field_info['required'] = 'required' in input_tag.lower()
            
            # 提取验证规则
            validation_attrs = ['pattern', 'minlength', 'maxlength', 'min', 'max']
            for attr in validation_attrs:
                pattern = f'{attr}=["\']([^"\']*)["\']'
                match = re.search(pattern, input_tag, re.IGNORECASE)
                if match:
                    field_info[attr] = match.group(1)
            
            # 只返回有名称的字段
            if field_info.get('name'):
                return field_info
            
        except Exception as e:
            logger.warning(f"解析输入字段失败: {e}")
        
        return None
    
    def detect_api_endpoints(self, html_content: str) -> List[Dict]:
        """检测API端点"""
        logger.info("检测API端点...")
        
        endpoints = []
        
        try:
            # 查找JavaScript中的API调用
            js_patterns = [
                r'fetch\(["\']([^"\']*)["\']',
                r'axios\.(?:get|post|put|delete)\(["\']([^"\']*)["\']',
                r'\.ajax\(\s*{[^}]*url\s*:\s*["\']([^"\']*)["\']',
                r'XMLHttpRequest.*open\(["\']([^"\']*)["\'],\s*["\']([^"\']*)["\']'
            ]
            
            for pattern in js_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple):
                        method, url = match
                        endpoints.append({'method': method.upper(), 'url': url})
                    else:
                        endpoints.append({'method': 'GET', 'url': match})
            
            # 查找表单action
            form_actions = re.findall(r'<form[^>]*action=["\']([^"\']*)["\']', html_content, re.IGNORECASE)
            for action in form_actions:
                endpoints.append({'method': 'POST', 'url': action, 'type': 'form'})
            
            logger.info(f"检测到 {len(endpoints)} 个API端点")
            
        except Exception as e:
            logger.error(f"检测API端点失败: {e}")
        
        return endpoints
    
    def analyze_csrf_protection(self, html_content: str) -> Optional[Dict]:
        """分析CSRF保护机制"""
        logger.info("分析CSRF保护机制...")
        
        csrf_info = {
            'has_csrf': False,
            'token_name': None,
            'token_value': None,
            'token_location': None
        }
        
        try:
            # 查找CSRF token
            csrf_patterns = [
                r'<input[^>]*name=["\']([^"\']*csrf[^"\']*)["\'][^>]*value=["\']([^"\']*)["\']',
                r'<input[^>]*value=["\']([^"\']*)["\'][^>]*name=["\']([^"\']*csrf[^"\']*)["\']',
                r'<meta[^>]*name=["\']([^"\']*csrf[^"\']*)["\'][^>]*content=["\']([^"\']*)["\']',
                r'csrf[_-]?token["\']?\s*[:=]\s*["\']([^"\']*)["\']'
            ]
            
            for pattern in csrf_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                if matches:
                    csrf_info['has_csrf'] = True
                    for match in matches:
                        if len(match) == 2:
                            name, value = match
                            if 'csrf' in name.lower():
                                csrf_info['token_name'] = name
                                csrf_info['token_value'] = value
                                csrf_info['token_location'] = 'form'
                                break
                    break
            
            if csrf_info['has_csrf']:
                logger.info(f"检测到CSRF保护: {csrf_info['token_name']}")
            else:
                logger.info("未检测到CSRF保护")
                
        except Exception as e:
            logger.error(f"分析CSRF保护失败: {e}")
        
        return csrf_info
    
    def detect_captcha_system(self, html_content: str) -> Optional[Dict]:
        """检测验证码系统"""
        logger.info("检测验证码系统...")
        
        captcha_info = {
            'has_captcha': False,
            'captcha_type': None,
            'captcha_site_key': None
        }
        
        try:
            # 检测reCAPTCHA
            if 'recaptcha' in html_content.lower():
                captcha_info['has_captcha'] = True
                captcha_info['captcha_type'] = 'reCAPTCHA'
                
                # 提取site key
                site_key_pattern = r'data-sitekey=["\']([^"\']*)["\']'
                match = re.search(site_key_pattern, html_content)
                if match:
                    captcha_info['captcha_site_key'] = match.group(1)
            
            # 检测hCaptcha
            elif 'hcaptcha' in html_content.lower():
                captcha_info['has_captcha'] = True
                captcha_info['captcha_type'] = 'hCaptcha'
                
                site_key_pattern = r'data-sitekey=["\']([^"\']*)["\']'
                match = re.search(site_key_pattern, html_content)
                if match:
                    captcha_info['captcha_site_key'] = match.group(1)
            
            # 检测图片验证码
            elif any(keyword in html_content.lower() for keyword in ['captcha', 'verification', 'verify']):
                if re.search(r'<img[^>]*captcha', html_content, re.IGNORECASE):
                    captcha_info['has_captcha'] = True
                    captcha_info['captcha_type'] = 'Image'
            
            if captcha_info['has_captcha']:
                logger.info(f"检测到验证码系统: {captcha_info['captcha_type']}")
            else:
                logger.info("未检测到验证码系统")
                
        except Exception as e:
            logger.error(f"检测验证码系统失败: {e}")
        
        return captcha_info
    
    def save_analysis_result(self, result: Dict, filename: str = "fifa_analysis_result.json"):
        """保存分析结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"分析结果已保存到: {filename}")
        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")

if __name__ == "__main__":
    analyzer = FIFAProtocolAnalyzer()
    
    # 执行分析
    result = analyzer.analyze_registration_flow()
    
    # 保存结果
    analyzer.save_analysis_result(result)
    
    # 显示摘要
    print("\n" + "="*50)
    print("FIFA协议分析结果摘要")
    print("="*50)
    
    if result['success']:
        print(f"✅ 注册页面: {result['registration_page']['url'] if result['registration_page'] else '未找到'}")
        print(f"✅ API端点数量: {len(result['api_endpoints'])}")
        print(f"✅ 必填字段数量: {len(result['required_fields'])}")
        print(f"✅ CSRF保护: {'是' if result['csrf_protection'] and result['csrf_protection']['has_csrf'] else '否'}")
        print(f"✅ 验证码系统: {result['captcha_info']['captcha_type'] if result['captcha_info'] and result['captcha_info']['has_captcha'] else '无'}")
    else:
        print("❌ 分析失败")
        if 'error' in result:
            print(f"错误信息: {result['error']}")
    
    print("\n详细结果已保存到 fifa_analysis_result.json")
